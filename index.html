<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="个性化一周食谱规划网站，支持16:8间歇性断食，专为中国用户优化的健康饮食管理工具" />
  <meta name="keywords" content="食谱规划,间歇性断食,16:8,健康饮食,减肥食谱,营养搭配,一周食谱" />
  <meta name="author" content="Weekly Meal Planner Team" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:title" content="个性化一周食谱规划 - 16:8间歇性断食" />
  <meta property="og:description" content="科学的一周食谱规划，支持16:8间歇性断食，帮助您健康减重" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:title" content="个性化一周食谱规划 - 16:8间歇性断食" />
  <meta property="twitter:description" content="科学的一周食谱规划，支持16:8间歇性断食，帮助您健康减重" />
  
  <title>个性化一周食谱规划 - 16:8间歇性断食</title>
  
  <!-- 使用中国本地字体，避免Google Fonts -->
  <style>
    @font-face {
      font-family: 'PingFang SC';
      src: local('PingFang SC Regular'), local('PingFangSC-Regular');
    }
    
    body {
      font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
    }
    
    /* 加载动画 */
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: linear-gradient(to bottom, #f9fafb, #ffffff);
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e5e7eb;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      margin-left: 16px;
      color: #6b7280;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div id="root">
    <!-- 加载动画 -->
    <div class="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载食谱规划器...</div>
    </div>
  </div>
  
  <!-- 使用本地模块加载，避免CDN依赖 -->
  <script type="module" src="/src/main.jsx"></script>
  
  <!-- 百度统计代码位置（如需要） -->
  <!-- <script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?YOUR_BAIDU_ANALYTICS_ID";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
  </script> -->
</body>
</html>

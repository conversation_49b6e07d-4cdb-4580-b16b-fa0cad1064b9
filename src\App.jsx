import { useMemo, useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Clock, Utensils, ShoppingCart, Printer, RefreshCw, CheckCircle2, Droplets, Moon, Sun, Download } from "lucide-react";

// 进食窗口默认：12:00 - 20:00
const DEFAULT_WINDOW = { start: "12:00", end: "20:00" };

// 扩展的食谱库，更符合中国人饮食习惯
const MEAL_BANK = {
  lunch: [
    {
      name: "鸡胸肉糙米能量碗",
      details: "鸡胸150g + 糙米100g(熟) + 西兰花300g + 橄榄油1茶勺",
      calories: 600,
      protein: 45,
      tags: ["高蛋白", "低脂"]
    },
    {
      name: "清蒸鳕鱼全麦面",
      details: "鳕鱼150g + 全麦面100g(熟) + 小番茄/菠菜300g",
      calories: 580,
      protein: 40,
      tags: ["清淡", "营养"]
    },
    {
      name: "牛肉藜麦沙拉",
      details: "瘦牛肉120g + 藜麦80g(熟) + 生菜/彩椒/黄瓜300g + 牛油果1/2",
      calories: 650,
      protein: 38,
      tags: ["饱腹", "营养丰富"]
    },
    {
      name: "三文鱼燕麦饭",
      details: "三文鱼150g + 燕麦饭100g(熟) + 西蓝花/芦笋300g",
      calories: 620,
      protein: 42,
      tags: ["Omega-3", "抗氧化"]
    },
    {
      name: "白切鸡胸配紫薯",
      details: "白切鸡胸150g + 蒸紫薯100g + 清炒菠菜300g",
      calories: 590,
      protein: 44,
      tags: ["传统", "清淡"]
    },
    {
      name: "蒸蛋羹配瘦肉",
      details: "鸡蛋羹3个 + 瘦肉丝80g + 清炒小白菜300g",
      calories: 560,
      protein: 38,
      tags: ["嫩滑", "易消化"]
    }
  ],
  snack: [
    {
      name: "酸奶坚果拼",
      details: "无糖酸奶200ml + 杏仁/核桃10颗 + 蓝莓一小碗",
      calories: 350,
      protein: 18,
      tags: ["益生菌", "抗氧化"]
    },
    {
      name: "鸡蛋双拼",
      details: "水煮蛋2个 + 苹果1个",
      calories: 260,
      protein: 14,
      tags: ["简单", "便携"]
    },
    {
      name: "奶酪配胡萝卜条",
      details: "低脂奶酪40g + 胡萝卜/黄瓜条200g",
      calories: 220,
      protein: 16,
      tags: ["低卡", "爽脆"]
    },
    {
      name: "豆浆配坚果",
      details: "无糖豆浆250ml + 混合坚果15g",
      calories: 280,
      protein: 16,
      tags: ["植物蛋白", "中式"]
    }
  ],
  dinner: [
    {
      name: "虾仁豆腐煲",
      details: "虾仁120g + 嫩豆腐200g + 青菜200g（少油）",
      calories: 430,
      protein: 36,
      tags: ["清淡", "高蛋白"]
    },
    {
      name: "鸡蛋羹配蔬菜",
      details: "鸡蛋2-3个蒸蛋 + 清炒时蔬250g（少油少盐）",
      calories: 380,
      protein: 28,
      tags: ["嫩滑", "营养"]
    },
    {
      name: "白鱼蔬菜盘",
      details: "白鱼150g + 大份生菜/沙拉300g + 橄榄油1茶勺",
      calories: 420,
      protein: 34,
      tags: ["低脂", "清爽"]
    },
    {
      name: "冬瓜排骨汤",
      details: "瘦排骨100g + 冬瓜300g + 清汤（去油）",
      calories: 350,
      protein: 32,
      tags: ["汤品", "去水肿"]
    }
  ],
};

const DAYS = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];

// 本地存储键名
const STORAGE_KEYS = {
  WINDOW: 'meal_planner_window',
  WATER: 'meal_planner_water',
  SHOPPING: 'meal_planner_shopping',
  SEED: 'meal_planner_seed',
  THEME: 'meal_planner_theme'
};

function useWeeklyPlan() {
  const [seed, setSeed] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.SEED);
    return saved ? parseInt(saved) : 0;
  });
  
  const plan = useMemo(() => {
    // 基于 seed 轮换菜单
    const pick = (arr, i) => arr[(i + seed) % arr.length];
    return DAYS.map((_, i) => ({
      lunch: pick(MEAL_BANK.lunch, i),
      snack: pick(MEAL_BANK.snack, i),
      dinner: pick(MEAL_BANK.dinner, i),
    }));
  }, [seed]);
  
  const reshuffle = () => {
    const newSeed = seed + 1;
    setSeed(newSeed);
    localStorage.setItem(STORAGE_KEYS.SEED, newSeed.toString());
  };
  
  return { plan, reshuffle };
}

function MacroBadge({ label, value }) {
  return (
    <span className="px-2 py-1 rounded-xl text-xs bg-gray-100 dark:bg-gray-800 whitespace-nowrap">
      {label}: <span className="font-semibold">{value}</span>
    </span>
  );
}

function Section({ title, icon, children, className = "" }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 12 }}
      animate={{ opacity: 1, y: 0 }}
      className={`card p-5 ${className}`}
    >
      <div className="flex items-center gap-2 mb-3">
        {icon}
        <h2 className="text-lg font-semibold">{title}</h2>
      </div>
      {children}
    </motion.div>
  );
}

function ThemeToggle({ isDark, onToggle }) {
  return (
    <button
      onClick={onToggle}
      className="p-2 rounded-xl border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
      title={isDark ? "切换到浅色模式" : "切换到深色模式"}
    >
      {isDark ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
    </button>
  );
}

export default function App() {
  const { plan, reshuffle } = useWeeklyPlan();
  
  // 状态管理
  const [window, setWindow] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.WINDOW);
    return saved ? JSON.parse(saved) : DEFAULT_WINDOW;
  });
  
  const [water, setWater] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.WATER);
    return saved ? parseInt(saved) : 0;
  });
  
  const [shopping, setShopping] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.SHOPPING);
    return saved ? JSON.parse(saved) : {};
  });
  
  const [isDark, setIsDark] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.THEME);
    return saved ? JSON.parse(saved) : false;
  });

  // 保存到本地存储
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.WINDOW, JSON.stringify(window));
  }, [window]);
  
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.WATER, water.toString());
  }, [water]);
  
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.SHOPPING, JSON.stringify(shopping));
  }, [shopping]);
  
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.THEME, JSON.stringify(isDark));
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDark]);

  const totals = useMemo(() => {
    let cal = 0, pro = 0;
    plan.forEach((d) => {
      cal += d.lunch.calories + d.snack.calories + d.dinner.calories;
      pro += d.lunch.protein + d.snack.protein + d.dinner.protein;
    });
    return { calories: cal, protein: pro };
  }, [plan]);

  const shoppingList = useMemo(() => {
    const items = new Set();
    plan.forEach((d) => {
      [d.lunch, d.snack, d.dinner].forEach((m) => {
        m.details.split(/[+，,]/).forEach((p) => items.add(p.trim()));
      });
    });
    return Array.from(items).filter(Boolean);
  }, [plan]);

  const toggleItem = (item) =>
    setShopping((s) => ({ ...s, [item]: !s[item] }));

  // 导出功能
  const exportData = () => {
    const data = {
      plan,
      window,
      totals,
      shoppingList,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `食谱计划_${new Date().toLocaleDateString('zh-CN')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-950 dark:to-gray-900 text-gray-900 dark:text-gray-100 p-4 md:p-8 transition-colors duration-300">
      <div className="max-w-6xl mx-auto">
        {/* 头部 */}
        <header className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              个性化一周食谱规划
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">16:8间歇性断食 · 科学营养搭配</p>
          </div>
          <ThemeToggle isDark={isDark} onToggle={() => setIsDark(!isDark)} />
        </header>
        
        <div className="grid md:grid-cols-3 gap-6">
          {/* 左侧：控制区 */}
          <div className="md:col-span-1 flex flex-col gap-6">
            <Section
              title="进食窗口 (16:8)"
              icon={<Clock className="w-5 h-5 text-blue-500" />}
            >
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-xs text-gray-500 dark:text-gray-400">开始时间</label>
                  <input
                    type="time"
                    value={window.start}
                    onChange={(e) => setWindow({ ...window, start: e.target.value })}
                    className="input mt-1"
                  />
                </div>
                <div>
                  <label className="text-xs text-gray-500 dark:text-gray-400">结束时间</label>
                  <input
                    type="time"
                    value={window.end}
                    onChange={(e) => setWindow({ ...window, end: e.target.value })}
                    className="input mt-1"
                  />
                </div>
              </div>
              <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <span className="font-medium">推荐时间：</span>{DEFAULT_WINDOW.start} - {DEFAULT_WINDOW.end}
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  禁食期内仅喝白水、无糖茶或黑咖啡
                </p>
              </div>
            </Section>

            <Section
              title="本周营养汇总"
              icon={<Utensils className="w-5 h-5 text-green-500" />}
            >
              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-xl">
                  <div className="text-2xl font-bold text-orange-500">{totals.calories}</div>
                  <div className="text-xs text-gray-500">总热量(kcal)</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-xl">
                  <div className="text-2xl font-bold text-blue-500">{totals.protein}</div>
                  <div className="text-xs text-gray-500">总蛋白(g)</div>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <button onClick={() => window.print()} className="btn-primary flex-1">
                  <Printer className="w-4 h-4" /> 打印
                </button>
                <button onClick={exportData} className="btn-secondary">
                  <Download className="w-4 h-4" />
                </button>
                <button onClick={reshuffle} className="btn-secondary">
                  <RefreshCw className="w-4 h-4" />
                </button>
              </div>
              
              <div className="mt-4 space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>中午主食控制在 80–120g（熟重）</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>每日蛋白质目标 ≥120g</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span>烹饪方式：蒸/煮/炖，少油少盐</span>
                </div>
              </div>
            </Section>

            <Section title="喝水打卡" icon={<Droplets className="w-5 h-5 text-cyan-500" />}>
              <div className="grid grid-cols-5 gap-2 mb-3">
                {[...Array(10)].map((_, i) => (
                  <button
                    key={i}
                    onClick={() => setWater((w) => (w === i + 1 ? i : i + 1))}
                    className={`aspect-square rounded-xl border-2 flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                      water > i 
                        ? "bg-cyan-500 border-cyan-500 text-white" 
                        : "border-gray-300 dark:border-gray-600 hover:border-cyan-300"
                    }`}
                    title={`第${i + 1}杯水 (约250ml)`}
                  >
                    {i + 1}
                  </button>
                ))}
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-cyan-600 dark:text-cyan-400">
                  {water * 250}ml / 2500ml
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  目标：每日 2–3L 白水（8–12 格）
                </div>
              </div>
            </Section>
          </div>

          {/* 右侧：周计划 + 购物清单 */}
          <div className="md:col-span-2 flex flex-col gap-6">
            <Section 
              title={`一周食谱（${window.start}-${window.end}）`} 
              icon={<CheckCircle2 className="w-5 h-5 text-emerald-500" />}
            >
              <div className="grid sm:grid-cols-2 gap-4">
                {plan.map((day, idx) => (
                  <motion.div 
                    key={idx} 
                    className="card p-4 hover:shadow-md transition-shadow duration-200"
                    whileHover={{ y: -2 }}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-lg">{DAYS[idx]}</h3>
                      <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-full">
                        {window.start} - {window.end}
                      </span>
                    </div>
                    
                    <div className="space-y-4 text-sm">
                      {[
                        { time: "12:00", type: "午餐", meal: day.lunch, color: "orange" },
                        { time: "16:00", type: "加餐", meal: day.snack, color: "blue" },
                        { time: "19:00", type: "晚餐", meal: day.dinner, color: "green" }
                      ].map(({ time, type, meal, color }) => (
                        <div key={type} className="border-l-4 border-gray-200 dark:border-gray-700 pl-3">
                          <div className="flex items-center gap-2 mb-1">
                            <span className={`w-2 h-2 bg-${color}-500 rounded-full`}></span>
                            <span className="font-medium">{time} {type}</span>
                          </div>
                          <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                            {meal.name}
                          </div>
                          <div className="text-gray-600 dark:text-gray-400 text-xs mb-2">
                            {meal.details}
                          </div>
                          <div className="flex flex-wrap gap-1">
                            <MacroBadge label="kcal" value={meal.calories} />
                            <MacroBadge label="蛋白" value={`${meal.protein}g`} />
                            {meal.tags && meal.tags.map(tag => (
                              <span key={tag} className="px-1.5 py-0.5 text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </Section>

            <Section title="购物清单（本周）" icon={<ShoppingCart className="w-5 h-5 text-purple-500" />}>
              <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-3">
                {shoppingList.map((item) => (
                  <motion.label 
                    key={item} 
                    className="flex items-start gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded-xl cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <input
                      type="checkbox"
                      checked={!!shopping[item]}
                      onChange={() => toggleItem(item)}
                      className="mt-1 w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className={`text-sm ${shopping[item] ? "line-through opacity-60" : ""}`}>
                      {item}
                    </span>
                  </motion.label>
                ))}
              </div>
              <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                已完成：{Object.values(shopping).filter(Boolean).length} / {shoppingList.length} 项
              </div>
            </Section>

            <Section title="执行要点（快速减肚子）" icon={<Utensils className="w-5 h-5 text-red-500" />} className="no-print">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2 text-green-600 dark:text-green-400">饮食要点</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>严格遵守进食窗口，<strong>禁食期零热量</strong></span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>主食减半：<strong>午餐80–120g，晚餐≤50g</strong></span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>蛋白优先：确保<strong>每日≥120g蛋白</strong></span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>蔬菜≥500g/日，烹饪少油少盐</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2 text-blue-600 dark:text-blue-400">运动建议</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>饭后快走30分钟</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>每周5次有氧 + 2次力量训练</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>记录体重、腰围变化</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span>腰围月目标：-3～5cm</span>
                    </li>
                  </ul>
                </div>
              </div>
            </Section>
          </div>
        </div>

        <footer className="mt-8 text-xs text-gray-500 dark:text-gray-400 text-center px-2 no-print">
          <p>本页面仅作一般健康建议，若有基础疾病或用药，请先咨询医生。</p>
          <p className="mt-1">© 2024 个性化食谱规划器 · 专为中国用户优化</p>
        </footer>
      </div>
    </div>
  );
}

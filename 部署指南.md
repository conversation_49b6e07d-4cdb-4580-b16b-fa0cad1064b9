# 个性化一周食谱规划网站 - 部署指南

## 🚀 快速部署（推荐）

### 方案一：直接使用 standalone.html
最简单的部署方式，无需构建工具：

1. **直接上传**：将 `standalone.html` 文件上传到任何静态网站托管服务
2. **重命名**：将文件重命名为 `index.html`
3. **访问**：通过域名直接访问

**优点**：
- ✅ 无需构建过程
- ✅ 使用国内CDN资源，加载速度快
- ✅ 兼容性好，支持所有现代浏览器
- ✅ 文件小，加载快速

### 方案二：使用 Vite 构建版本
如果需要更多自定义功能：

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build

# 上传 dist 目录到服务器
```

## 🇨🇳 国内部署平台推荐

### 1. 阿里云 OSS + CDN
**最推荐的部署方案**

```bash
# 1. 开通阿里云OSS服务
# 2. 创建存储桶，设置为静态网站托管
# 3. 上传文件到OSS
# 4. 配置CDN加速
# 5. 绑定自定义域名
```

**优势**：
- 🚀 国内访问速度极快
- 💰 成本低廉
- 🔒 稳定可靠
- 📊 详细的访问统计

### 2. 腾讯云 COS + CDN
```bash
# 1. 开通腾讯云COS服务
# 2. 创建存储桶，开启静态网站功能
# 3. 上传文件
# 4. 配置CDN
```

### 3. 七牛云存储
```bash
# 1. 注册七牛云账号
# 2. 创建对象存储空间
# 3. 上传文件
# 4. 配置自定义域名
```

### 4. 华为云 OBS
```bash
# 1. 开通华为云OBS服务
# 2. 创建桶，配置静态网站托管
# 3. 上传文件
# 4. 配置CDN加速
```

## 🌍 国际部署平台

### 1. Vercel（推荐）
```bash
# 1. 安装 Vercel CLI
npm i -g vercel

# 2. 部署
vercel

# 3. 按提示配置
```

### 2. Netlify
```bash
# 1. 拖拽文件到 netlify.com
# 2. 或使用 Git 连接自动部署
```

### 3. GitHub Pages
```bash
# 1. 创建 GitHub 仓库
# 2. 上传代码
# 3. 在设置中启用 Pages
```

## 🔧 服务器部署

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /var/www/meal-planner;
    index index.html;
    
    # 启用 gzip 压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### Apache 配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/meal-planner
    
    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
    </Location>
    
    # 缓存设置
    <FilesMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
</VirtualHost>
```

## 🐳 Docker 部署

### Dockerfile
```dockerfile
FROM nginx:alpine

# 复制文件
COPY standalone.html /usr/share/nginx/html/index.html

# 复制 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### docker-compose.yml
```yaml
version: '3.8'
services:
  meal-planner:
    build: .
    ports:
      - "80:80"
    restart: unless-stopped
```

### 部署命令
```bash
# 构建镜像
docker build -t meal-planner .

# 运行容器
docker run -d -p 80:80 --name meal-planner meal-planner

# 或使用 docker-compose
docker-compose up -d
```

## 📱 移动端优化

### PWA 支持
在 `standalone.html` 中添加：

```html
<!-- 在 head 中添加 -->
<link rel="manifest" href="/manifest.json">
<meta name="theme-color" content="#3b82f6">

<!-- Service Worker -->
<script>
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js');
}
</script>
```

### manifest.json
```json
{
  "name": "个性化一周食谱规划",
  "short_name": "食谱规划",
  "description": "16:8间歇性断食食谱规划工具",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#3b82f6",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

## 🔍 SEO 优化

### 1. 添加结构化数据
```html
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "个性化一周食谱规划",
  "description": "16:8间歇性断食食谱规划工具",
  "url": "https://your-domain.com",
  "applicationCategory": "HealthApplication",
  "operatingSystem": "Any"
}
</script>
```

### 2. 添加 sitemap.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://your-domain.com/</loc>
    <lastmod>2024-01-01</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>
```

## 📊 分析和监控

### 百度统计
```html
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?YOUR_SITE_ID";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
```

### 腾讯分析
```html
<script>
(function(){
var mta = document.createElement("script");
mta.src = "//pingjs.qq.com/h5/stats.js?v2.0.4";
mta.setAttribute("name", "MTAH5");
mta.setAttribute("sid", "YOUR_SITE_ID");
var s = document.getElementsByTagName("script")[0];
s.parentNode.insertBefore(mta, s);
})();
</script>
```

## 🚨 注意事项

### 1. 域名备案
如果使用国内服务器，需要进行ICP备案：
- 阿里云备案：https://beian.aliyun.com/
- 腾讯云备案：https://cloud.tencent.com/product/ba

### 2. HTTPS 配置
建议启用 HTTPS：
- 申请免费SSL证书（Let's Encrypt）
- 配置强制HTTPS重定向

### 3. 性能优化
- 启用 gzip 压缩
- 配置浏览器缓存
- 使用 CDN 加速
- 压缩图片资源

### 4. 安全设置
```nginx
# 安全头设置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
```

## 📞 技术支持

如果在部署过程中遇到问题，可以：

1. 查看项目 README.md
2. 检查浏览器控制台错误信息
3. 确认所有依赖文件都已正确上传
4. 验证服务器配置是否正确

---

**祝您部署顺利！** 🎉

import { useMemo, useState } from "react";
import { motion } from "framer-motion";
import { Clock, Utensils, ShoppingCart, Printer, RefreshCw, CheckCircle2, Droplets } from "lucide-react";

// 进食窗口默认：12:00 - 20:00
const DEFAULT_WINDOW = { start: "12:00", end: "20:00" };

const MEAL_BANK = {
  lunch: [
    {
      name: "鸡胸肉糙米能量碗",
      details: "鸡胸150g + 糙米100g(熟) + 西兰花300g + 橄榄油1茶勺",
      calories: 600,
      protein: 45,
    },
    {
      name: "清蒸鳕鱼全麦面",
      details: "鳕鱼150g + 全麦面100g(熟) + 小番茄/菠菜300g",
      calories: 580,
      protein: 40,
    },
    {
      name: "牛肉藜麦沙拉",
      details: "瘦牛肉120g + 藜麦80g(熟) + 生菜/彩椒/黄瓜300g + 牛油果1/2",
      calories: 650,
      protein: 38,
    },
    {
      name: "三文鱼燕麦饭",
      details: "三文鱼150g + 燕麦饭100g(熟) + 西蓝花/芦笋300g",
      calories: 620,
      protein: 42,
    },
  ],
  snack: [
    {
      name: "酸奶坚果拼",
      details: "无糖酸奶200ml + 杏仁/核桃10颗 + 蓝莓一小碗",
      calories: 350,
      protein: 18,
    },
    {
      name: "鸡蛋双拼",
      details: "水煮蛋2个 + 苹果1个",
      calories: 260,
      protein: 14,
    },
    {
      name: "奶酪配胡萝卜条",
      details: "低脂奶酪40g + 胡萝卜/黄瓜条200g",
      calories: 220,
      protein: 16,
    },
  ],
  dinner: [
    {
      name: "虾仁豆腐煲",
      details: "虾仁120g + 嫩豆腐200g + 青菜200g（少油）",
      calories: 430,
      protein: 36,
    },
    {
      name: "鸡蛋羹配蔬菜",
      details: "鸡蛋2-3个蒸蛋 + 清炒时蔬250g（少油少盐）",
      calories: 380,
      protein: 28,
    },
    {
      name: "白鱼蔬菜盘",
      details: "白鱼150g + 大份生菜/沙拉300g + 橄榄油1茶勺",
      calories: 420,
      protein: 34,
    },
  ],
};

const DAYS = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];

function useWeeklyPlan() {
  const [seed, setSeed] = useState(0);
  const plan = useMemo(() => {
    // 基于 seed 轮换菜单
    const pick = (arr, i) => arr[(i + seed) % arr.length];
    return DAYS.map((_, i) => ({
      lunch: pick(MEAL_BANK.lunch, i),
      snack: pick(MEAL_BANK.snack, i),
      dinner: pick(MEAL_BANK.dinner, i),
    }));
  }, [seed]);
  return { plan, reshuffle: () => setSeed((s) => s + 1) };
}

function MacroBadge({ label, value }: { label: string; value: number }) {
  return (
    <span className="px-2 py-1 rounded-xl text-xs bg-gray-100 dark:bg-gray-800">
      {label}: <span className="font-semibold">{value}</span>
    </span>
  );
}

function Section({ title, icon, children }: any) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 12 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-900 rounded-2xl shadow-sm p-5"
    >
      <div className="flex items-center gap-2 mb-3">
        {icon}
        <h2 className="text-lg font-semibold">{title}</h2>
      </div>
      {children}
    </motion.div>
  );
}

export default function App() {
  const { plan, reshuffle } = useWeeklyPlan();
  const [window, setWindow] = useState(DEFAULT_WINDOW);
  const [water, setWater] = useState(0);
  const [shopping, setShopping] = useState<Record<string, boolean>>({});

  const totals = useMemo(() => {
    let cal = 0,
      pro = 0;
    plan.forEach((d) => {
      cal += d.lunch.calories + d.snack.calories + d.dinner.calories;
      pro += d.lunch.protein + d.snack.protein + d.dinner.protein;
    });
    return { calories: cal, protein: pro };
  }, [plan]);

  const shoppingList = useMemo(() => {
    const items = new Set<string>();
    plan.forEach((d) => {
      [d.lunch, d.snack, d.dinner].forEach((m) => {
        m.details.split(/[+，,]/).forEach((p) => items.add(p.trim()));
      });
    });
    return Array.from(items).filter(Boolean);
  }, [plan]);

  const toggleItem = (item: string) =>
    setShopping((s) => ({ ...s, [item]: !s[item] }));

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-950 dark:to-gray-900 text-gray-900 dark:text-gray-100 p-4 md:p-8">
      <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-6">
        {/* 左侧：控制区 */}
        <div className="md:col-span-1 flex flex-col gap-6">
          <Section
            title="进食窗口 (16:8)"
            icon={<Clock className="w-5 h-5" />}
          >
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="text-xs text-gray-500">开始</label>
                <input
                  type="time"
                  value={window.start}
                  onChange={(e) => setWindow({ ...window, start: e.target.value })}
                  className="mt-1 w-full rounded-xl border px-3 py-2 bg-transparent"
                />
              </div>
              <div>
                <label className="text-xs text-gray-500">结束</label>
                <input
                  type="time"
                  value={window.end}
                  onChange={(e) => setWindow({ ...window, end: e.target.value })}
                  className="mt-1 w-full rounded-xl border px-3 py-2 bg-transparent"
                />
              </div>
            </div>
            <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
              建议：<span className="font-medium">{DEFAULT_WINDOW.start} - {DEFAULT_WINDOW.end}</span>
              ，禁食期内仅喝白水、无糖茶或黑咖啡。
            </p>
          </Section>

          <Section
            title="本周汇总"
            icon={<Utensils className="w-5 h-5" />}
          >
            <div className="flex flex-wrap items-center gap-2">
              <MacroBadge label="总热量(kcal)" value={totals.calories} />
              <MacroBadge label="总蛋白(g)" value={totals.protein} />
            </div>
            <div className="mt-3 flex gap-2">
              <button
                onClick={() => window.print()}
                className="inline-flex items-center gap-2 px-3 py-2 rounded-xl bg-gray-900 text-white dark:bg-gray-100 dark:text-gray-900"
              >
                <Printer className="w-4 h-4" /> 打印/导出
              </button>
              <button
                onClick={reshuffle}
                className="inline-flex items-center gap-2 px-3 py-2 rounded-xl border"
              >
                <RefreshCw className="w-4 h-4" /> 换一组菜单
              </button>
            </div>
            <ul className="mt-4 text-sm list-disc ml-5 space-y-1">
              <li>中午主食控制在 80–120g（熟重），晚上尽量不吃主食。</li>
              <li>蛋白质目标：每天 ≥120g（体重较高阶段优先保肌）。</li>
              <li>烹饪方式：蒸/煮/炖/空气炸，少油少盐。</li>
            </ul>
          </Section>

          <Section title="喝水打卡" icon={<Droplets className="w-5 h-5" />}>
            <div className="flex items-center gap-2 flex-wrap">
              {[...Array(10)].map((_, i) => (
                <button
                  key={i}
                  onClick={() => setWater((w) => (w === i + 1 ? i : i + 1))}
                  className={`w-8 h-8 rounded-full border flex items-center justify-center ${
                    water > i ? "bg-gray-900 text-white dark:bg-gray-100 dark:text-gray-900" : ""
                  }`}
                  title="每格约250ml"
                >
                  {i + 1}
                </button>
              ))}
            </div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              目标：每日 2–3L 白水（8–12 格）。
            </p>
          </Section>
        </div>

        {/* 右侧：周计划 + 购物清单 */}
        <div className="md:col-span-2 flex flex-col gap-6">
          <Section title="一周食谱（12:00-20:00）" icon={<CheckCircle2 className="w-5 h-5" />}>
            <div className="grid sm:grid-cols-2 gap-4">
              {plan.map((day, idx) => (
                <div key={idx} className="rounded-2xl border p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{DAYS[idx]}</h3>
                    <span className="text-xs text-gray-500">进食：{window.start} - {window.end}</span>
                  </div>
                  <div className="space-y-3 text-sm">
                    <div>
                      <div className="font-medium">12:00 午餐</div>
                      <div className="opacity-90">{day.lunch.name}</div>
                      <div className="text-gray-500">{day.lunch.details}</div>
                      <div className="mt-1 flex gap-2 text-xs">
                        <MacroBadge label="kcal" value={day.lunch.calories} />
                        <MacroBadge label="蛋白(g)" value={day.lunch.protein} />
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">16:00 加餐</div>
                      <div className="opacity-90">{day.snack.name}</div>
                      <div className="text-gray-500">{day.snack.details}</div>
                      <div className="mt-1 flex gap-2 text-xs">
                        <MacroBadge label="kcal" value={day.snack.calories} />
                        <MacroBadge label="蛋白(g)" value={day.snack.protein} />
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">19:00 晚餐</div>
                      <div className="opacity-90">{day.dinner.name}</div>
                      <div className="text-gray-500">{day.dinner.details}</div>
                      <div className="mt-1 flex gap-2 text-xs">
                        <MacroBadge label="kcal" value={day.dinner.calories} />
                        <MacroBadge label="蛋白(g)" value={day.dinner.protein} />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Section>

          <Section title="购物清单（本周）" icon={<ShoppingCart className="w-5 h-5" />}>
            <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-3">
              {shoppingList.map((item) => (
                <label key={item} className="flex items-start gap-2 p-3 border rounded-xl cursor-pointer">
                  <input
                    type="checkbox"
                    checked={!!shopping[item]}
                    onChange={() => toggleItem(item)}
                    className="mt-1"
                  />
                  <span className={shopping[item] ? "line-through opacity-60" : ""}>{item}</span>
                </label>
              ))}
            </div>
          </Section>

          <Section title="执行要点（快速减肚子）" icon={<Utensils className="w-5 h-5" />}>
            <ul className="list-disc ml-5 space-y-2 text-sm">
              <li>严格遵守进食窗口；<b>禁食期零热量</b>（水/无糖茶/黑咖啡）。</li>
              <li>主食减半：<b>午餐80–120g（熟）</b>，<b>晚餐 ≤50g 或不吃</b>。</li>
              <li>蛋白优先：鸡胸/鱼/虾/蛋/豆腐，确保<b>每日 ≥120g 蛋白</b>。</li>
              <li>蔬菜 ≥500g/日；烹饪少油少盐；杜绝含糖饮料与酒精。</li>
              <li>饭后快走30分钟；每周至少5次有氧 + 2次力量（自重即可）。</li>
              <li>每周记录：体重、腰围、进度照；腰围每月目标 -3～5cm。</li>
            </ul>
          </Section>
        </div>
      </div>

      <footer className="max-w-6xl mx-auto mt-8 text-xs text-gray-500 dark:text-gray-400 px-2">
        本页面仅作一般健康建议，若有基础疾病或用药，请先咨询医生。
      </footer>
    </div>
  );
}

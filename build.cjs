#!/usr/bin/env node

/**
 * 简单的构建脚本，用于生成部署文件
 * Simple build script for deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建个性化食谱规划网站...');

// 创建 dist 目录
const distDir = path.join(__dirname, 'dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir);
  console.log('✅ 创建 dist 目录');
}

// 复制 standalone.html 到 dist/index.html
const standaloneHtml = path.join(__dirname, 'standalone.html');
const indexHtml = path.join(distDir, 'index.html');

if (fs.existsSync(standaloneHtml)) {
  fs.copyFileSync(standaloneHtml, indexHtml);
  console.log('✅ 复制 standalone.html -> dist/index.html');
} else {
  console.error('❌ 找不到 standalone.html 文件');
  process.exit(1);
}

// 创建 manifest.json
const manifest = {
  "name": "个性化一周食谱规划",
  "short_name": "食谱规划",
  "description": "16:8间歇性断食食谱规划工具，专为中国用户优化",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#3b82f6",
  "lang": "zh-CN",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "any maskable"
    },
    {
      "src": "/icon-512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "any maskable"
    }
  ]
};

fs.writeFileSync(
  path.join(distDir, 'manifest.json'),
  JSON.stringify(manifest, null, 2)
);
console.log('✅ 生成 manifest.json');

// 创建 robots.txt
const robots = `User-agent: *
Allow: /

Sitemap: https://your-domain.com/sitemap.xml`;

fs.writeFileSync(path.join(distDir, 'robots.txt'), robots);
console.log('✅ 生成 robots.txt');

// 创建 sitemap.xml
const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://your-domain.com/</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

fs.writeFileSync(path.join(distDir, 'sitemap.xml'), sitemap);
console.log('✅ 生成 sitemap.xml');

// 创建简单的 Service Worker
const serviceWorker = `
const CACHE_NAME = 'meal-planner-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json'
];

self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});
`;

fs.writeFileSync(path.join(distDir, 'sw.js'), serviceWorker);
console.log('✅ 生成 Service Worker');

// 创建 .htaccess 文件（Apache 服务器）
const htaccess = `# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# 安全头
<IfModule mod_headers.c>
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "no-referrer-when-downgrade"
</IfModule>

# SPA 路由支持
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    RewriteRule ^index\\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>`;

fs.writeFileSync(path.join(distDir, '.htaccess'), htaccess);
console.log('✅ 生成 .htaccess');

// 创建部署说明
const deploymentInfo = `# 部署文件说明

此目录包含了网站的所有部署文件：

## 文件列表
- \`index.html\` - 主页面文件
- \`manifest.json\` - PWA 应用清单
- \`sw.js\` - Service Worker 文件
- \`robots.txt\` - 搜索引擎爬虫规则
- \`sitemap.xml\` - 网站地图
- \`.htaccess\` - Apache 服务器配置

## 部署步骤
1. 将此目录下的所有文件上传到服务器根目录
2. 确保服务器支持静态文件托管
3. 如果使用自定义域名，请修改 sitemap.xml 中的域名
4. 访问您的网站测试功能

## 注意事项
- 如果使用 Nginx，请参考部署指南中的配置示例
- 建议启用 HTTPS 以获得更好的安全性
- 可以添加网站统计代码进行访问分析

构建时间：${new Date().toLocaleString('zh-CN')}
`;

fs.writeFileSync(path.join(distDir, 'README.md'), deploymentInfo);
console.log('✅ 生成部署说明');

console.log('\n🎉 构建完成！');
console.log('📁 部署文件位于 dist/ 目录');
console.log('🚀 您可以将 dist/ 目录下的所有文件上传到服务器');
console.log('\n📖 详细部署说明请查看 部署指南.md');

// 显示文件大小统计
const files = fs.readdirSync(distDir);
let totalSize = 0;

console.log('\n📊 文件大小统计：');
files.forEach(file => {
  const filePath = path.join(distDir, file);
  const stats = fs.statSync(filePath);
  const sizeKB = (stats.size / 1024).toFixed(2);
  totalSize += stats.size;
  console.log(`   ${file}: ${sizeKB} KB`);
});

console.log(`   总计: ${(totalSize / 1024).toFixed(2)} KB`);
console.log('\n✨ 网站已优化，加载速度快，适合中国网络环境！');

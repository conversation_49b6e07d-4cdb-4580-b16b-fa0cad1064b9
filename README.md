# 个性化一周食谱规划网站

专为中国用户优化的16:8间歇性断食食谱规划工具，支持个性化营养搭配和智能购物清单生成。

## ✨ 功能特色

- 🍽️ **智能食谱规划**：基于16:8间歇性断食原理的科学营养搭配
- 📊 **营养数据统计**：实时计算热量、蛋白质等营养指标
- 🛒 **智能购物清单**：自动生成本周所需食材清单
- 💧 **喝水打卡**：每日饮水量追踪和提醒
- 🌙 **深色模式**：支持浅色/深色主题切换
- 💾 **数据持久化**：本地存储用户设置和进度
- 🖨️ **打印导出**：支持打印和JSON数据导出
- 📱 **响应式设计**：完美适配手机、平板、电脑

## 🚀 快速开始

### 环境要求

- Node.js 16+ 
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 查看应用

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🏗️ 项目结构

```
├── index.html          # HTML入口文件
├── package.json        # 项目配置
├── vite.config.js      # Vite构建配置
├── tailwind.config.js  # Tailwind CSS配置
├── postcss.config.js   # PostCSS配置
└── src/
    ├── main.jsx        # React入口文件
    ├── App.jsx         # 主应用组件
    └── index.css       # 全局样式
```

## 🎯 中国优化特性

### 网络优化
- ✅ 使用本地字体，避免Google Fonts
- ✅ 优化资源加载，减少外部依赖
- ✅ 支持国内CDN部署
- ✅ 针对中国网络环境优化构建配置

### 本地化特性
- ✅ 完整中文界面
- ✅ 符合中国人饮食习惯的食谱
- ✅ 中式烹饪方法和食材
- ✅ 本地化的营养建议

### 用户体验
- ✅ 简洁直观的中文界面
- ✅ 移动端优先的响应式设计
- ✅ 符合中国用户习惯的交互方式
- ✅ 支持微信等社交平台分享

## 📋 使用说明

### 1. 设置进食窗口
在左侧控制面板设置您的进食时间窗口，默认推荐12:00-20:00（16:8模式）。

### 2. 查看食谱规划
系统会自动生成一周的营养均衡食谱，包括午餐、加餐和晚餐。

### 3. 管理购物清单
右侧会自动生成本周所需的食材清单，可以勾选已购买的物品。

### 4. 喝水打卡
每天记录饮水量，目标是2-3升（8-12杯）。

### 5. 数据导出
可以打印食谱或导出JSON数据文件保存。

## 🔧 技术栈

- **前端框架**：React 18
- **构建工具**：Vite
- **样式框架**：Tailwind CSS
- **动画库**：Framer Motion
- **图标库**：Lucide React
- **开发语言**：JavaScript (ES6+)

## 📱 部署建议

### 静态网站部署
推荐部署到以下平台：
- 阿里云OSS + CDN
- 腾讯云COS + CDN
- 七牛云存储
- Vercel（国际版）
- Netlify（国际版）

### 服务器部署
```bash
# 构建项目
npm run build

# 将dist目录上传到服务器
# 配置nginx或apache服务器
```

### Docker部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本应用仅提供一般性健康建议，不能替代专业医疗建议。如有基础疾病或正在服药，请在使用前咨询医生。

## 📞 联系我们

如有问题或建议，欢迎通过以下方式联系：

- 📧 邮箱：<EMAIL>
- 🐛 问题反馈：[GitHub Issues](https://github.com/your-repo/issues)

---

**专为中国用户打造的智能食谱规划工具** 🇨🇳

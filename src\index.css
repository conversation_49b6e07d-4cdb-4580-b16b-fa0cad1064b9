@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* 中文字体优化 */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
  }
}

@layer components {
  /* 自定义按钮样式 */
  .btn-primary {
    @apply inline-flex items-center gap-2 px-4 py-2 rounded-xl bg-gray-900 text-white dark:bg-gray-100 dark:text-gray-900 font-medium transition-all duration-200 hover:scale-105 active:scale-95;
  }
  
  .btn-secondary {
    @apply inline-flex items-center gap-2 px-4 py-2 rounded-xl border border-gray-300 dark:border-gray-600 font-medium transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-gray-900 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-800;
  }
  
  /* 输入框样式 */
  .input {
    @apply w-full rounded-xl border border-gray-300 dark:border-gray-600 px-3 py-2 bg-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }
}

@layer utilities {
  /* 打印样式 */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-break {
      page-break-before: always;
    }
    
    body {
      background: white !important;
      color: black !important;
    }
    
    .card {
      border: 1px solid #e5e7eb !important;
      box-shadow: none !important;
    }
  }
  
  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
  }
}
